import { useState, useRef, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Toolt<PERSON> } from "flowbite-react"
import type { VideoItemProps } from "@/types"
import Hls from 'hls.js';
import { setRequestHeadersForExtension, cleanupRequestHeaders } from "@/utils/chromeMessages"
import { getDownloaderUrl } from "@/utils/config"
// 导入图标
import {
  IconPlayerPlayFilled,
  IconCopy,
  IconDownload,
  IconCheck
} from "@tabler/icons-react"

export default function VideoItem({
  video,
  showThumbnail = false,
  className = "",
  onPlayClick,
  downloaded = false,
  onDownloadClick
}: VideoItemProps) {
  const { t } = useTranslation()
  const [copySuccess, setCopySuccess] = useState(false)
  const [isDownloadHovered, setIsDownloadHovered] = useState(false)

  // video元素引用
  const videoRef = useRef<HTMLVideoElement>(null);

  // 处理HLS实例生命周期和请求头设置
  useEffect(() => {
    if (showThumbnail && videoRef.current) {
      const videoElement = videoRef.current;
      let hls: Hls | null = null;

      // 检测M3U8格式
      const isM3u8 = video.url.includes('.m3u8') ||
        video.url.includes('application/vnd.apple.mpegurl') ||
        video.url.includes('application/x-mpegurl');

      // 设置请求头的异步函数
      const setupVideoWithHeaders = async () => {
        try {
          // 为扩展页面设置请求头（如果有的话）
          if (video.requestHeaders && video.requestHeaders.length > 0) {
            console.log(`为预览设置请求头，URL: ${video.url}`)
            await setRequestHeadersForExtension(
              video.requestId,
              video.url,
              video.requestHeaders,
              video.pageUrl
            )
          }

          // 设置视频源
          if (isM3u8) {
            // M3U8流处理
            if (Hls.isSupported()) {
              hls = new Hls();
              hls.attachMedia(videoElement);
              hls.on(Hls.Events.MANIFEST_PARSED, () => {
                // 可在此处控制自动播放
                // videoElement.play();
              });
              hls.loadSource(video.url);
            }
            // Safari原生HLS支持
            else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
              videoElement.src = video.url;
            }
          } else {
            // 标准媒体文件
            videoElement.src = video.url;
          }
        } catch (error) {
          console.error('设置预览请求头失败:', error)
          // 即使请求头设置失败，也尝试直接加载视频
          if (isM3u8) {
            if (Hls.isSupported()) {
              hls = new Hls();
              hls.attachMedia(videoElement);
              hls.loadSource(video.url);
            } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
              videoElement.src = video.url;
            }
          } else {
            videoElement.src = video.url;
          }
        }
      }

      // 执行设置
      setupVideoWithHeaders()

      // 清理函数
      return () => {
        // 清理HLS实例，防止内存泄漏
        if (hls) {
          hls.destroy();
        }

        // 清理请求头规则
        if (video.requestHeaders && video.requestHeaders.length > 0) {
          cleanupRequestHeaders(video.requestId).catch(error => {
            console.error('清理预览请求头失败:', error)
          })
        }
      };
    }
  }, [video.url, video.requestId, showThumbnail]);

  // 复制视频链接
  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(video.url)
      setCopySuccess(true)
      // 2秒后重置状态
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 处理下载
  const handleDownload = async () => {
    if (!video.requestId) return

    // 通知父组件更新下载状态
    onDownloadClick?.()

    try {
      // 检测M3U8格式
      const isM3u8 = video.url.includes('.m3u8') ||
        video.url.includes('application/vnd.apple.mpegurl') ||
        video.url.includes('application/x-mpegurl') ||
        video.ext?.toLowerCase() === 'm3u8';

      // 为M3U8格式添加特殊标识
      const downloadData = {
        ...video,
        filename: video.title, // 确保外部下载页面能够正确获取文件名
        isM3u8: isM3u8
      };

      // 将视频数据存储到Chrome storage，供外部页面访问
      await chrome.storage.local.set({
        [`downloadData_${video.requestId}`]: downloadData
      })

      // 打开外部下载器页面（独立的 React 项目）
      const downloaderUrl = getDownloaderUrl(video.requestId)
      window.open(downloaderUrl, '_blank')

      console.log(`外部下载器已打开 - ${isM3u8 ? 'M3U8流媒体' : '标准媒体'}下载`)
    } catch (error) {
      console.error('下载失败:', error)
    }
  }
  return (
    <div className={`flex flex-col justify-center items-start p-2 gap-2 w-full bg-white rounded-lg ${className}`}>
      {/* 标题行 */}
      <div className="flex flex-row items-center gap-1 w-full">
        {video.favIconUrl && (
          <div className="w-4 h-4 flex-shrink-0">
            <img
              src={video.favIconUrl}
              alt={t('videoItem.platformIcon')}
              className="w-full h-full object-contain rounded-sm"
            />
          </div>
        )}
        <span className="text-xs font-medium text-gray-900 flex-1 truncate" title={video.title}>
          {video.title}
        </span>
      </div>

      {/* 信息和操作行 */}
      <div className="flex flex-row items-center gap-2 w-full">
        <span className="text-xs font-medium text-gray-500 flex-1" title={video.size}>
          {video.size}
        </span>

        <div className="flex flex-row items-center gap-1">
          {/* 复制按钮 */}
          <Tooltip
            content={copySuccess ? t('videoItem.copySuccess') : t('videoItem.copyVideoLink')}
            style="dark"
            className="!h-8 !px-3 !bg-gray-800 !rounded !inline-flex !justify-start !items-center !text-white !text-sm !font-medium !font-['Inter'] !leading-tight"
          >
            <button
              className="flex justify-center items-center w-6 h-6 rounded-full hover:bg-gray-100"
              onClick={handleCopyUrl}
            >
              {copySuccess ? (
                <IconCheck className="w-4 h-4 text-blue-700" />
              ) : (
                <IconCopy className="w-4 h-4 text-gray-900" />
              )}
            </button>
          </Tooltip>

          {/* 预览按钮 */}
          <Tooltip
            content={t('videoItem.previewVideo')}
            style="dark"
            className="!h-8 !px-3 !bg-gray-800 !rounded !inline-flex !justify-start !items-center !text-white !text-sm !font-medium !font-['Inter'] !leading-tight"
          >
            <button
              className="flex justify-center items-center w-6 h-6 rounded-full hover:bg-gray-100"
              onClick={onPlayClick}
            >
              <IconPlayerPlayFilled className="w-4 h-4 text-gray-900" />
            </button>
          </Tooltip>

          {/* 下载按钮 */}
          <Tooltip
            content={
              downloaded
                ? t('videoItem.downloadCompleted')
                : t('videoItem.downloadFile')
            }
            style="dark"
            className="!h-8 !px-3 !bg-gray-800 !rounded !inline-flex !justify-start !items-center !text-white !text-sm !font-medium !font-['Inter'] !leading-tight"
          >
            <button
              className="flex justify-center items-center w-6 h-6 rounded-full hover:bg-gray-100"
              onClick={handleDownload}
              disabled={false}
              onMouseEnter={() => setIsDownloadHovered(true)}
              onMouseLeave={() => setIsDownloadHovered(false)}
            >
              {downloaded && !isDownloadHovered ? (
                <IconCheck className="w-4 h-4 text-blue-700" />
              ) : (
                <IconDownload className="w-4 h-4 text-blue-700" />
              )}
            </button>
          </Tooltip>
        </div>
      </div>

      {/* 缩略图区域 */}
      {showThumbnail && (
        <>
          <div className="flex flex-col items-center gap-2.5 w-full">
            {/* 使用video标签处理预览，使用hls.js处理m3u8文件 */}
            <video
              ref={videoRef}
              controls
              preload="metadata"
            />
          </div>
          {video.pageTitle && (
            <div className="text-xs font-medium text-gray-500 text-center w-full px-2">
              <p className="line-clamp-2" title={video.pageTitle}>
                {video.pageTitle}
              </p>
            </div>
          )}
        </>
      )}
    </div>
  )
} 